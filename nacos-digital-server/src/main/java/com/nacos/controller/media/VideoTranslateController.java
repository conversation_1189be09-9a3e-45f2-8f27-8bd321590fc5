package com.nacos.controller.media;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.vo.VideoTranslateStatusVO;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.MediaType;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.Map;

/**
 * 视频翻译控制器
 *
 */
@Slf4j
@Tag(name = "视频翻译")
@RestController
@RequestMapping("/api/v1/media/video/translate")
@Validated
@RequiredArgsConstructor
public class VideoTranslateController {

    private final VideoTranslateService videoTranslateService;

    /**
     * 提交视频翻译任务
     * URL: /api/v1/media/video/translate/submit
     *
     * @param request 翻译请求参数
     * @param file    视频文件（必填）
     * @return 任务提交结果
     */
    @Operation(summary = "提交视频翻译任务", description = "提交视频文件进行语言翻译处理")
    @PostMapping(value = "/submit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, Object>> submitTranslateTask(
            @Parameter(name = "request", description = "翻译请求参数", required = true) @RequestPart(value = "request") @Valid VideoTranslateRequestDTO request,
            @Parameter(name = "file", description = "视频文件", required = true) @RequestPart(value = "file") MultipartFile file) {
        String methodName = "submitTranslateTask";
        try {
            // 1. 文件必填验证
            if (file == null || file.isEmpty()) {
                log.warn("[{}] 文件验证失败: 视频文件不能为空, userId={}", methodName, request.getUserId());
                return Result.ERROR("视频文件不能为空");
            }

            // 2. 调用服务层提交任务
            Result<Map<String, Object>> result = videoTranslateService.submitTranslateTask(request, file);

            if (result.isSuccess()) {
                log.info("[{}] 视频翻译任务提交成功: userId={}, taskId={}",
                        methodName, request.getUserId(), result.getData().get("taskId"));
            } else {
                log.error("[{}] 视频翻译任务提交失败: userId={}, error={}",
                        methodName, request.getUserId(), result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 提交视频翻译任务异常: userId={}, error={}", methodName, request.getUserId(), e.getMessage(), e);
            return Result.ERROR("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     * URL: /api/v1/media/video/translate/status/{taskId}
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Operation(summary = "查询任务状态", description = "根据任务ID查询视频翻译任务的当前状态和进度")
    @GetMapping("/status/{taskId}")
    public Result<VideoTranslateStatusVO> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 查询任务状态: taskId={}", methodName, taskId);

            // 调用服务层查询状态
            Result<VideoTranslateStatusVO> result = videoTranslateService.getTaskStatus(taskId);

            if (result.isSuccess()) {
                log.info("[{}] 查询任务状态成功: taskId={}, status={}", methodName, taskId, result.getData().getStatus());
            } else {
                log.error("[{}] 查询任务状态失败: taskId={}, error={}", methodName, taskId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态失败: " + e.getMessage());
        }
    }
}
