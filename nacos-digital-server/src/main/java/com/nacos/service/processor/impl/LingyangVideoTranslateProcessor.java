package com.nacos.service.processor.impl;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.model.SoundView.SoundViewApiUtil;
import com.nacos.result.Result;
import com.nacos.service.processor.VideoTranslateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 羚羊平台视频翻译处理器
 * 
 * <p>负责处理羚羊平台的视频翻译请求，实现VideoTranslateProcessor接口。
 * 该处理器复用现有的SoundViewApiUtil工具类，实现参数转换和API调用。</p>
 * 
 * <h3>主要功能</h3>
 * <ul>
 *   <li>将通用VideoTranslateRequestDTO转换为羚羊平台特定的请求参数</li>
 *   <li>调用SoundViewApiUtil进行视频翻译任务提交和状态查询</li>
 *   <li>将羚羊平台响应转换为通用的VideoTranslateResult</li>
 *   <li>处理羚羊平台特定的错误情况和异常</li>
 * </ul>
 * 
 * <h3>支持的功能</h3>
 * <ul>
 *   <li><strong>视频翻译</strong>：支持多种语言对的视频翻译</li>
 *   <li><strong>音色选择</strong>：支持多种音色配置</li>
 *   <li><strong>任务管理</strong>：支持任务提交、状态查询、取消等操作</li>
 *   <li><strong>健康检查</strong>：支持系统环境检查和服务可用性验证</li>
 * </ul>
 * 
 * <h3>优先级设置</h3>
 * <p>作为主要服务商，羚羊平台处理器设置为最高优先级（1），确保在多服务商环境中优先使用。</p>
 * 
 * <AUTHOR>
 * @since 2025-01-29
 * @version 1.0
 */
@Slf4j
@Component
public class LingyangVideoTranslateProcessor implements VideoTranslateProcessor {

    /**
     * 处理羚羊平台视频翻译请求
     * 
     * @param requestDTO 视频翻译请求参数
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    public Result<VideoTranslateResult> processRequest(VideoTranslateRequestDTO requestDTO, String userId) {
        String methodName = "processRequest";
        log.info("[{}] 羚羊平台视频翻译开始，userId={}, videoUrl={}, {}→{}", 
                methodName, userId, requestDTO.getVideoUrl(), 
                requestDTO.getSourceLanguage(), requestDTO.getTargetLanguage());

        try {
            // 1. 参数验证
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败，userId={}", methodName, userId);
                return Result.ERROR("参数验证失败");
            }

            // 2. 调用羚羊平台API提交视频翻译任务
            Result<Map<String, Object>> apiResult = SoundViewApiUtil.submitVideoTranslation(
                    requestDTO.getVideoUrl(),
                    requestDTO.getSourceLanguage(),
                    requestDTO.getTargetLanguage(),
                    requestDTO.getVoiceId(),
                    requestDTO.getTaskName(),
                    userId
            );

            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] 羚羊平台API调用失败，userId={}, error={}", methodName, userId, apiResult.getMessage());
                return Result.ERROR("羚羊平台视频翻译失败：" + apiResult.getMessage());
            }

            // 3. 响应转换：Map<String, Object> -> VideoTranslateResult
            VideoTranslateResult result = convertToVideoTranslateResult(apiResult.getData(), requestDTO);

            log.info("[{}] 羚羊平台视频翻译任务提交成功，userId={}, taskId={}, providerTaskId={}", 
                    methodName, userId, result.getTaskId(), result.getProviderTaskId());

            return Result.SUCCESS("羚羊平台视频翻译任务提交成功", result);
            
        } catch (IllegalArgumentException e) {
            log.warn("[{}] 参数错误，userId={}, error={}", methodName, userId, e.getMessage());
            return Result.ERROR("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("[{}] 羚羊平台视频翻译异常，userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("羚羊平台视频翻译异常：" + e.getMessage());
        }
    }

    /**
     * 检查视频翻译任务状态
     * 
     * @param providerTaskId 羚羊平台任务ID
     * @return 任务状态结果
     */
    @Override
    public Result<VideoTranslateResult> checkTaskStatus(String providerTaskId) {
        String methodName = "checkTaskStatus";
        log.info("[{}] 查询羚羊平台任务状态，providerTaskId={}", methodName, providerTaskId);

        try {
            // 调用羚羊平台API查询任务状态
            Result<Map<String, Object>> apiResult = SoundViewApiUtil.getTaskStatus(providerTaskId);

            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] 羚羊平台状态查询失败，providerTaskId={}, error={}", 
                         methodName, providerTaskId, apiResult.getMessage());
                return Result.ERROR("查询任务状态失败：" + apiResult.getMessage());
            }

            // 响应转换：Map<String, Object> -> VideoTranslateResult
            VideoTranslateResult result = convertStatusToVideoTranslateResult(apiResult.getData());

            log.info("[{}] 羚羊平台任务状态查询成功，providerTaskId={}, status={}, progress={}", 
                    methodName, providerTaskId, result.getStatus(), result.getProgress());

            return Result.SUCCESS("任务状态查询成功", result);
            
        } catch (Exception e) {
            log.error("[{}] 羚羊平台状态查询异常，providerTaskId={}, error={}", 
                     methodName, providerTaskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态异常：" + e.getMessage());
        }
    }

    /**
     * 获取服务商名称
     * 
     * @return 服务商标识
     */
    @Override
    public String getProviderName() {
        return "LINGYANG";
    }

    /**
     * 检查是否支持指定服务商
     * 
     * @param provider 服务商标识
     * @return true表示支持，false表示不支持
     */
    @Override
    public boolean isSupported(String provider) {
        return "LINGYANG".equalsIgnoreCase(provider);
    }

    /**
     * 获取服务商优先级
     * 
     * @return 优先级数值，羚羊平台作为主要服务商设置为最高优先级
     */
    @Override
    public int getPriority() {
        return 1; // 最高优先级
    }

    /**
     * 获取服务商描述信息
     * 
     * @return 服务商描述
     */
    @Override
    public String getDescription() {
        return "羚羊平台视频翻译服务";
    }

    /**
     * 验证羚羊平台特定的请求参数
     * 
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @return true表示参数有效，false表示参数无效
     */
    @Override
    public boolean isValidRequest(VideoTranslateRequestDTO requestDTO, String userId) {
        // 调用父接口的基础验证
        if (!VideoTranslateProcessor.super.isValidRequest(requestDTO, userId)) {
            return false;
        }
        
        // 羚羊平台特定验证
        // 1. 检查视频URL格式（羚羊平台要求HTTPS）
        String videoUrl = requestDTO.getVideoUrl();
        if (!videoUrl.toLowerCase().startsWith("https://")) {
            log.warn("羚羊平台要求视频URL必须使用HTTPS协议: {}", videoUrl);
            return false;
        }
        
        // 2. 检查语言对支持（羚羊平台支持的语言对）
        String sourceLanguage = requestDTO.getSourceLanguage();
        String targetLanguage = requestDTO.getTargetLanguage();
        if (!isLanguagePairSupported(sourceLanguage, targetLanguage)) {
            log.warn("羚羊平台不支持该语言对: {} → {}", sourceLanguage, targetLanguage);
            return false;
        }
        
        // 3. 检查音色ID格式（羚羊平台音色ID通常为数字）
        String voiceId = requestDTO.getVoiceId();
        try {
            Integer.parseInt(voiceId);
        } catch (NumberFormatException e) {
            log.warn("羚羊平台音色ID必须为数字: {}", voiceId);
            return false;
        }
        
        // 4. 检查音色ID是否必填（当不使用原声时）
        if (requestDTO.isVoiceIdRequired() && (voiceId == null || voiceId.trim().isEmpty())) {
            log.warn("羚羊平台需要音色ID，但未提供");
            return false;
        }

        return true;
    }

    /**
     * 检查语言对是否支持
     * 
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return true表示支持，false表示不支持
     */
    private boolean isLanguagePairSupported(String sourceLanguage, String targetLanguage) {
        // 羚羊平台支持的语言列表
        String[] supportedLanguages = {"cn", "en", "ja", "ko", "fr", "de", "es", "ru", "it", "pt", "ar", "th", "vi", "hi", "ms"};
        
        boolean sourceSupported = false;
        boolean targetSupported = false;
        
        for (String lang : supportedLanguages) {
            if (lang.equals(sourceLanguage)) {
                sourceSupported = true;
            }
            if (lang.equals(targetLanguage)) {
                targetSupported = true;
            }
        }
        
        return sourceSupported && targetSupported;
    }

    /**
     * 检查视频格式是否支持
     * 
     * @param videoFormat 视频格式
     * @return true表示支持，false表示不支持
     */
    private boolean isSupportedVideoFormat(String videoFormat) {
        // 羚羊平台支持的视频格式
        String[] supportedFormats = {"mp4", "avi", "mov", "wmv", "flv", "mkv"};
        
        for (String format : supportedFormats) {
            if (format.equalsIgnoreCase(videoFormat)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 将羚羊平台提交响应转换为VideoTranslateResult
     * 
     * @param apiResponse 羚羊平台API响应
     * @param requestDTO 原始请求DTO
     * @return VideoTranslateResult
     */
    private VideoTranslateResult convertToVideoTranslateResult(Map<String, Object> apiResponse, VideoTranslateRequestDTO requestDTO) {
        VideoTranslateResult result = new VideoTranslateResult();
        
        // 设置任务ID信息
        String taskId = (String) apiResponse.get("taskId");
        result.setTaskId(taskId);
        result.setProviderTaskId((String) apiResponse.get("lingyangTaskId"));
        
        // 设置状态信息
        result.setStatus((String) apiResponse.get("status"));
        result.setProgress(0); // 刚提交的任务进度为0
        
        // 设置服务商信息
        result.setProviderName(getProviderName());
        
        // 设置时间信息
        Object submitTime = apiResponse.get("submitTime");
        if (submitTime instanceof Long) {
            result.setCreateTime((Long) submitTime);
        }
        
        return result;
    }

    /**
     * 将羚羊平台状态响应转换为VideoTranslateResult
     * 
     * @param statusResponse 羚羊平台状态响应
     * @return VideoTranslateResult
     */
    private VideoTranslateResult convertStatusToVideoTranslateResult(Map<String, Object> statusResponse) {
        VideoTranslateResult result = new VideoTranslateResult();
        
        // 设置任务ID信息
        result.setTaskId((String) statusResponse.get("taskId"));
        result.setProviderTaskId((String) statusResponse.get("lingyangTaskId"));
        
        // 设置状态信息
        result.setStatus((String) statusResponse.get("status"));
        
        // 设置进度信息
        Object progress = statusResponse.get("progress");
        if (progress instanceof Integer) {
            result.setProgress((Integer) progress);
        } else if (progress instanceof String) {
            try {
                result.setProgress(Integer.parseInt((String) progress));
            } catch (NumberFormatException e) {
                result.setProgress(0);
            }
        }
        
        // 设置结果URL
        result.setResultVideoUrl((String) statusResponse.get("resultVideoUrl"));
        
        // 设置错误信息（如果有）
        Object isCompleted = statusResponse.get("isCompleted");
        Object isFailed = statusResponse.get("isFailed");
        
        if (Boolean.TRUE.equals(isFailed)) {
            result.setErrorMessage((String) statusResponse.get("statusDesc"));
        }
        
        // 设置服务商信息
        result.setProviderName(getProviderName());
        
        return result;
    }
}
